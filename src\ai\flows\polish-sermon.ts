'use server';

/**
 * @fileOverview An AI agent that polishes sermon drafts for clarity, coherence, and impact.
 *
 * - polishSermon - A function that handles the sermon polishing process.
 * - PolishSermonInput - The input type for the polishSermon function.
 * - PolishSermonOutput - The return type for the polishSermon function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { GenerateSermonOutlineOutput } from './generate-sermon-outline';

const PolishSermonInputSchema = z.object({
  sermon: z.custom<GenerateSermonOutlineOutput>(),
  systemPrompt: z.string().optional().describe('An optional system prompt to guide the polishing agent.'),
});
export type PolishSermonInput = z.infer<typeof PolishSermonInputSchema>;
export type PolishSermonOutput = GenerateSermonOutlineOutput;


export async function polishSermon(
  input: PolishSermonInput
): Promise<PolishSermonOutput> {
  const { sermon, systemPrompt } = input;

  const polishText = async (text: string) => {
    const { output } = await polishTextFlow({ text, systemPrompt });
    // It's possible for the model to return an invalid object, so we must handle it gracefully.
    return output?.polishedText ?? text;
  }

  const polishedIntroduction = await polishText(sermon.introduction);
  const polishedConclusion = await polishText(sermon.conclusion);
  const polishedPoints = await Promise.all(
    sermon.points.map(async (point) => {
      const polishedExplanation = await polishText(point.explanation);
      return { ...point, explanation: polishedExplanation };
    })
  );

  return {
    ...sermon,
    introduction: polishedIntroduction,
    points: polishedPoints,
    conclusion: polishedConclusion,
  };
}


const PolishTextInputSchema = z.object({
  text: z.string().describe('An HTML string to be polished.'),
  systemPrompt: z.string().optional(),
});

const PolishTextOutputSchema = z.object({
  polishedText: z.string().describe('The polished version of the text, with improved clarity, coherence, and grammar. The output should be a single HTML string.'),
});


const polishTextFlow = ai.defineFlow(
  {
    name: 'polishTextFlow',
    inputSchema: PolishTextInputSchema,
    outputSchema: PolishTextOutputSchema,
  },
  async ({ text, systemPrompt }) => {
    const prompt = ai.definePrompt({
      name: 'polishSermonTextPrompt',
      input: { schema: PolishTextInputSchema },
      output: { schema: PolishTextOutputSchema },
      prompt: `You are a meticulous editor AI with a deep understanding of theology and rhetoric. Your task is to polish a given piece of sermon text.

      {{{systemPrompt}}}
    
      Review the text provided below. It is an HTML string. Your goal is to enhance its quality without losing the original author's voice.
      - Correct any grammatical errors, spelling mistakes, and typos within the HTML content.
      - Improve sentence structure for better flow and clarity.
      - Ensure that all statements are coherent and logically sound.
      - Fix any unfinished or awkward-sounding sentences.
      - Do NOT change the core message or theological points.
      - Do NOT alter the HTML structure (e.g., p, i tags). Your edits should only modify the text content within these tags.
      - The output must be a single, clean HTML string, preserving the original's tags.
    
      Text to Polish:
      {{{text}}}
    
      Return only the polished text in the 'polishedText' field of the JSON output.
      `,
    });

    const { output } = await prompt({ text, systemPrompt });
    return output!;
  }
);
