
"use client";

import { useState, createContext, useContext } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { BrainCircuit, CheckCircle, Loader2 } from "lucide-react";
import { Badge } from "./ui/badge";

type Thought = {
  agent: string;
  thought: string;
  status: 'pending' | 'working' | 'complete';
};

type MindBarContextType = {
  thoughts: Thought[];
  setThoughts: React.Dispatch<React.SetStateAction<Thought[]>>;
};

const MindBarContext = createContext<MindBarContextType | null>(null);

export const MindBarProvider = ({ children }: { children: React.ReactNode }) => {
  const [thoughts, setThoughts] = useState<Thought[]>([]);
  return (
    <MindBarContext.Provider value={{ thoughts, setThoughts }}>
      {children}
    </MindBarContext.Provider>
  );
};

export const useMindBar = () => {
  const context = useContext(MindBarContext);
  if (!context) {
    throw new Error("useMindBar must be used within a MindBarProvider");
  }
  return context;
};


export function MindBar() {
    const { thoughts } = useMindBar();

    const getStatusIcon = (status: Thought['status']) => {
        switch (status) {
            case 'working':
                return <Loader2 className="h-4 w-4 animate-spin text-primary" />;
            case 'complete':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'pending':
            default:
                return <div className="h-4 w-4 rounded-full border-2 border-muted-foreground" />;
        }
    };

    if (thoughts.length === 0) {
        return null;
    }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-6 right-24 z-50 rounded-full shadow-lg h-14 w-14"
          aria-label="Open AI Mind Bar"
        >
          <BrainCircuit className="h-6 w-6" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle className="font-headline flex items-center gap-2">
            <BrainCircuit /> AI Mind Bar
          </DialogTitle>
          <DialogDescription>
            A real-time look into the AI's thought process during generation.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6 py-4">
          {thoughts.map((thought, index) => (
            <div key={index} className="flex gap-4 items-start">
              <div className="w-5 flex-shrink-0 pt-1">{getStatusIcon(thought.status)}</div>
              <div>
                <p className="font-bold">{thought.agent}</p>
                <p className="text-muted-foreground">{thought.thought}</p>
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}
