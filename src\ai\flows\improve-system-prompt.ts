'use server';

/**
 * @fileOverview A flow to improve the system prompt using AI.
 *
 * - improveSystemPrompt - A function that takes a system prompt and returns an improved version.
 * - ImproveSystemPromptInput - The input type for the improveSystemPrompt function.
 * - ImproveSystemPromptOutput - The return type for the improveSystemPrompt function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ImproveSystemPromptInputSchema = z.object({
  prompt: z.string().describe('The system prompt to improve.'),
});
export type ImproveSystemPromptInput = z.infer<typeof ImproveSystemPromptInputSchema>;

const ImproveSystemPromptOutputSchema = z.object({
  improvedPrompt: z.string().describe('The improved system prompt.'),
});
export type ImproveSystemPromptOutput = z.infer<typeof ImproveSystemPromptOutputSchema>;

export async function improveSystemPrompt(input: ImproveSystemPromptInput): Promise<ImproveSystemPromptOutput> {
  return improveSystemPromptFlow(input);
}

const prompt = ai.definePrompt({
  name: 'improveSystemPromptPrompt',
  input: {schema: ImproveSystemPromptInputSchema},
  output: {schema: ImproveSystemPromptOutputSchema},
  prompt: `You are an AI prompt optimizer. Your task is to improve the given system prompt to be more clear, effective, and engaging.

Original Prompt: {{{prompt}}}

Improved Prompt:`, 
});

const improveSystemPromptFlow = ai.defineFlow(
  {
    name: 'improveSystemPromptFlow',
    inputSchema: ImproveSystemPromptInputSchema,
    outputSchema: ImproveSystemPromptOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
