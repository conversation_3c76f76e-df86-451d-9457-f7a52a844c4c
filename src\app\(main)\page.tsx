
"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { generateSermonOutline, type GenerateSermonOutlineOutput } from "@/ai/flows/generate-sermon-outline";
import { polishSermon } from "@/ai/flows/polish-sermon";
import { analyzeSermonDraft, type AnalyzeSermonDraftOutput } from "@/ai/flows/analyze-sermon-draft";
import { contextualInsights, type ContextualInsightsOutput } from "@/ai/flows/contextual-insights";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Wand2, Search, FileText, Save, Sparkles, Pencil } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useMindBar } from "@/components/mind-bar";

const sermonOutlineSchema = z.object({
  topicOrScripture: z.string().min(1, "Please enter a topic or scripture."),
  desiredTone: z.string().optional(),
  targetAudience: z.string().optional(),
  strictTopic: z.boolean().optional(),
});

type LoadingState = {
  sermon: boolean;
  polish: boolean;
  insights: boolean;
  analysis: boolean;
};

const sermonToHtmlString = (sermon: GenerateSermonOutlineOutput): string => {
  let html = `<h2>${sermon.title}</h2>`;
  html += `<h3>${sermon.mainText}</h3>`;
  html += `<h4>Introduction</h4>${sermon.introduction}`;
  sermon.points.forEach((p, i) => {
    html += `<h4>Point ${i + 1}: ${p.point}</h4>`;
    html += `<h5>${p.scripture}</h5>`;
    html += p.explanation;
  });
  html += `<h4>Conclusion</h4>${sermon.conclusion}`;
  return html;
};


const sermonToText = (sermon: GenerateSermonOutlineOutput | null): string => {
  if (!sermon) return "";
  
  const cleanHtml = (html: string) => {
    if (typeof document !== 'undefined') {
      const div = document.createElement("div");
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    }
    return html.replace(/<[^>]+>/g, '');
  }

  let text = `Title: ${sermon.title}\n`;
  text += `Main Text: ${sermon.mainText}\n\n`;
  text += `Introduction:\n${cleanHtml(sermon.introduction)}\n\n`;
  sermon.points.forEach((p, i) => {
    text += `Point ${i + 1}: ${p.point}\n`;
    text += `Scripture: ${p.scripture}\n`;
    text += `Explanation:\n${cleanHtml(p.explanation)}\n\n`;
  });
  text += `Conclusion:\n${cleanHtml(sermon.conclusion)}\n`;
  return text;
};

export default function SermonGeneratorPage() {
  const [originalSermon, setOriginalSermon] = useState<GenerateSermonOutlineOutput | null>(null);
  const [polishedSermon, setPolishedSermon] = useState<GenerateSermonOutlineOutput | null>(null);
  const [insights, setInsights] = useState<ContextualInsightsOutput | null>(null);
  const [analysis, setAnalysis] = useState<AnalyzeSermonDraftOutput | null>(null);
  const [isLoading, setIsLoading] = useState<LoadingState>({
    sermon: false,
    polish: false,
    insights: false,
    analysis: false,
  });
  const [sermonDraft, setSermonDraft] = useState("");
  const { toast } = useToast();
  const { setThoughts } = useMindBar();

  const form = useForm<z.infer<typeof sermonOutlineSchema>>({
    resolver: zodResolver(sermonOutlineSchema),
    defaultValues: {
      topicOrScripture: "",
      desiredTone: "encouraging",
      targetAudience: "A diverse, international congregation with Nigerian roots.",
      strictTopic: false,
    },
  });

  const handleGenerateSermon = async (values: z.infer<typeof sermonOutlineSchema>) => {
    setIsLoading(prev => ({ ...prev, sermon: true, polish: true, insights: false, analysis: false }));
    setOriginalSermon(null);
    setPolishedSermon(null);
    setInsights(null);
    setAnalysis(null);
    setSermonDraft("");
    setThoughts([
      { agent: "Sermon Generation Agent", thought: "Okay, let's craft a sermon on this topic. I need a title, main text, introduction, several points with explanations and scriptures, and a powerful conclusion. I will focus on the user's desired tone and audience.", status: 'working' },
      { agent: "Sermon Polishing Agent", thought: "Standing by. Once the draft is ready, I will review each section for clarity, grammar, and coherence.", status: 'pending' },
    ]);
    try {
      // Step 1: Generate the initial sermon
      const systemPrompt = localStorage.getItem("systemPrompt") || undefined;
      const result = await generateSermonOutline({ ...values, systemPrompt });
      setOriginalSermon(result);
      const sermonAsText = sermonToText(result);
      
      setThoughts([
        { agent: "Sermon Generation Agent", thought: "Draft complete. The structure is in place with all required sections.", status: 'complete' },
        { agent: "Sermon Polishing Agent", thought: "Receiving draft. I will now polish the text, section by section, to preserve the HTML structure.", status: 'working' },
      ]);
      setSermonDraft(sermonAsText); // Set draft for the text area
      setIsLoading(prev => ({ ...prev, sermon: false }));

      // Step 2: Polish the generated sermon
      const polishingSystemPrompt = localStorage.getItem("polishingSystemPrompt") || undefined;
      const polishResult = await polishSermon({ sermon: result, systemPrompt: polishingSystemPrompt });
      setPolishedSermon(polishResult);

       setThoughts([
        { agent: "Sermon Generation Agent", thought: "Draft complete. The structure is in place with all required sections.", status: 'complete' },
        { agent: "Sermon Polishing Agent", thought: "Polishing complete. I've refined each section of the sermon for clarity and impact.", status: 'complete' },
      ]);

    } catch (error) {
      console.error(error);
      toast({ variant: "destructive", title: "Error Generating Sermon", description: "An unexpected error occurred. The AI might have returned an invalid structure. Please try again." });
      setThoughts([]);
    } finally {
      setIsLoading(prev => ({ ...prev, sermon: false, polish: false }));
    }
  };

  const handleGetInsights = async () => {
    if (!sermonDraft) return;
    setIsLoading(prev => ({ ...prev, insights: true }));
    setInsights(null);
    try {
      const result = await contextualInsights({ sermonOutline: sermonDraft });
      setInsights(result);
    } catch (error) {
      toast({ variant: "destructive", title: "Error Getting Insights", description: "An unexpected error occurred. Please try again." });
    } finally {
      setIsLoading(prev => ({ ...prev, insights: false }));
    }
  };
  
  const handleAnalyzeDraft = async () => {
    if (!sermonDraft) return;
    setIsLoading(prev => ({ ...prev, analysis: true }));
    setAnalysis(null);
    try {
      const result = await analyzeSermonDraft({ draftText: sermonDraft });
      setAnalysis(result);
    } catch (error) {
      toast({ variant: "destructive", title: "Error Analyzing Draft", description: "An unexpected error occurred. Please try again." });
    } finally {
      setIsLoading(prev => ({ ...prev, analysis: false }));
    }
  };

  const currentSermon = polishedSermon ? polishedSermon : originalSermon;


  return (
    <div className="space-y-8">
      <header>
        <h1 className="text-4xl font-bold font-headline text-primary">Sermon Generator</h1>
        <p className="text-lg text-muted-foreground mt-2">Craft your next message with the help of AI.</p>
      </header>
      
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="font-headline text-2xl flex items-center gap-2"><Wand2 className="text-primary"/> 1. Generate Your Sermon</CardTitle>
          <CardDescription>Start by providing a topic or scripture, and let our AI craft a full, structured sermon for you.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleGenerateSermon)} className="space-y-6">
              <FormField
                control={form.control}
                name="topicOrScripture"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Topic or Scripture</FormLabel>
                    <Input placeholder="e.g., John 3:16 or 'The topic of grace'" {...field} />
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid md:grid-cols-2 gap-6">
                 <FormField
                  control={form.control}
                  name="desiredTone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">Desired Tone</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger><SelectValue placeholder="Select a tone" /></SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="encouraging">Encouraging</SelectItem>
                          <SelectItem value="challenging">Challenging</SelectItem>
                          <SelectItem value="inspirational">Inspirational</SelectItem>
                           <SelectItem value="expository">Expository</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
                 <FormField
                  control={form.control}
                  name="targetAudience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">Target Audience</FormLabel>
                      <Input placeholder="e.g., Young adults in Lagos" {...field} />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="strictTopic"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm bg-secondary/30">
                    <div className="space-y-0.5">
                      <FormLabel>Strict Topic Adherence</FormLabel>
                      <FormDescription>
                        If enabled, the AI will stick strictly to the topic you provided.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={isLoading.sermon || isLoading.polish} size="lg">
                {(isLoading.sermon || isLoading.polish) ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Wand2 className="mr-2 h-4 w-4" />}
                Generate Sermon
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      
      {(isLoading.sermon || isLoading.polish) && !currentSermon &&
        <Card className="shadow-md">
          <CardContent className="p-6">
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <div className="ml-4 space-y-1">
                <p className="font-semibold">{isLoading.sermon ? "Generating draft..." : "Polishing sermon..."}</p>
                <p className="text-sm text-muted-foreground">
                  {isLoading.sermon ? "The first agent is writing the sermon draft." : "The editor agent is refining the text."}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      }

      {currentSermon && (
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="font-headline text-2xl flex items-center gap-2"><FileText className="text-primary"/> 2. Refine & Analyze</CardTitle>
            <CardDescription>Here's your generated sermon. Review the structured content, then edit the full draft and use the AI tools for deeper analysis.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Tabs defaultValue="polished" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="original" disabled={!polishedSermon}>
                  <Pencil className="mr-2 h-4 w-4"/> Original Draft
                </TabsTrigger>
                <TabsTrigger value="polished" disabled={!polishedSermon}>
                   <Sparkles className="mr-2 h-4 w-4"/> Polished
                </TabsTrigger>
              </TabsList>
              <TabsContent value="original">
                 <div className="p-4 border rounded-lg bg-secondary/30 space-y-6 mt-4 prose max-w-none prose-p:my-2 prose-h2:text-primary prose-h3:text-primary prose-h4:text-primary">
                   <h2 className="text-2xl font-bold font-headline text-primary">{originalSermon?.title}</h2>
                   <p className="text-lg text-muted-foreground font-semibold">{originalSermon?.mainText}</p>
                   <blockquote className="border-l-4 border-primary pl-4" dangerouslySetInnerHTML={{ __html: originalSermon?.introduction || '' }} />
                   <Separator />
                   {originalSermon?.points.map((point, index) => (
                     <div key={index} className="p-4 not-prose">
                       <h3 className="text-xl font-bold font-headline">Point {index + 1}: {point.point}</h3>
                       <p className="font-semibold text-primary">{point.scripture}</p>
                       <div className="mt-2 prose prose-base max-w-none [&_p]:mb-4" dangerouslySetInnerHTML={{ __html: point.explanation }} />
                     </div>
                   ))}
                   <Separator />
                   <h3 className="text-xl font-bold font-headline">Conclusion</h3>
                   <div className="mt-2" dangerouslySetInnerHTML={{ __html: originalSermon?.conclusion || ''}} />
                 </div>
              </TabsContent>
               <TabsContent value="polished">
                <div className="p-4 border rounded-lg bg-secondary/30 space-y-6 mt-4 prose max-w-none prose-p:my-2 prose-h2:text-primary prose-h3:text-primary prose-h4:text-primary">
                  {isLoading.polish && !polishedSermon ? (
                     <div className="flex justify-center items-center p-8">
                       <Loader2 className="h-6 w-6 animate-spin text-primary" />
                       <p className="ml-4 text-muted-foreground">Polishing...</p>
                     </div>
                   ) : polishedSermon && (
                    <>
                      <h2 className="text-2xl font-bold font-headline text-primary">{polishedSermon?.title}</h2>
                      <p className="text-lg text-muted-foreground font-semibold">{polishedSermon?.mainText}</p>
                      <blockquote className="border-l-4 border-primary pl-4" dangerouslySetInnerHTML={{ __html: polishedSermon?.introduction || '' }} />
                      <Separator />
                      {polishedSermon?.points.map((point, index) => (
                        <div key={index} className="p-4 not-prose">
                          <h3 className="text-xl font-bold font-headline">Point {index + 1}: {point.point}</h3>
                          <p className="font-semibold text-primary">{point.scripture}</p>
                          <div className="mt-2 prose prose-base max-w-none [&_p]:mb-4" dangerouslySetInnerHTML={{ __html: point.explanation }} />
                        </div>
                      ))}
                      <Separator />
                      <h3 className="text-xl font-bold font-headline">Conclusion</h3>
                      <div className="mt-2" dangerouslySetInnerHTML={{ __html: polishedSermon?.conclusion || ''}} />
                    </>
                   )}
                </div>
               </TabsContent>
            </Tabs>
            
            
            <div>
              <Label htmlFor="sermon-draft" className="text-base font-medium">Full Sermon Draft (for Analysis)</Label>
              <Textarea
                id="sermon-draft"
                value={sermonDraft}
                onChange={(e) => setSermonDraft(e.target.value)}
                rows={20}
                className="mt-2 text-base"
                placeholder="Edit your sermon draft here..."
              />
            </div>
            
            <div className="flex flex-wrap gap-4">
              <Button onClick={handleGetInsights} disabled={isLoading.insights || !sermonDraft}>
                {isLoading.insights ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Search className="mr-2 h-4 w-4" />}
                Get Contextual Insights
              </Button>
               <Button onClick={handleAnalyzeDraft} disabled={isLoading.analysis || !sermonDraft}>
                {isLoading.analysis ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <FileText className="mr-2 h-4 w-4" />}
                Analyze Draft
              </Button>
            </div>
            
            {isLoading.insights && <div className="flex justify-center p-4"><Loader2 className="h-6 w-6 animate-spin text-primary" /></div>}
            {insights && (
              <Card className="shadow-inner bg-secondary/30">
                <CardHeader>
                  <CardTitle className="font-headline text-xl flex items-center gap-2"><Search /> Contextual Insights</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-bold text-base">Relevant Bible Passages</h4>
                    <p className="whitespace-pre-wrap mt-1">{insights.biblePassages}</p>
                  </div>
                  <Separator/>
                  <div>
                    <h4 className="font-bold text-base">Commentary Suggestions</h4>
                    <p className="whitespace-pre-wrap mt-1">{insights.commentarySuggestions}</p>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {isLoading.analysis && <div className="flex justify-center p-4"><Loader2 className="h-6 w-6 animate-spin text-primary" /></div>}
            {analysis && (
              <Card className="shadow-inner bg-secondary/30">
                <CardHeader>
                  <CardTitle className="font-headline text-xl flex items-center gap-2"><FileText /> Sermon Analysis</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div><Badge>Tone</Badge><p className="mt-2">{analysis.toneAnalysis}</p></div>
                  <div><Badge>Clarity</Badge><p className="mt-2">{analysis.clarityAnalysis}</p></div>
                  <div><Badge>Cultural Relevance</Badge><p className="mt-2">{analysis.culturalRelevanceAnalysis}</p></div>
                  <Separator/>
                  <div>
                    <h4 className="font-bold text-base">Overall Feedback</h4>
                    <p className="mt-1">{analysis.overallFeedback}</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </CardContent>
          <CardFooter>
            <Button size="lg"><Save className="mr-2 h-4 w-4" /> Save to Archive</Button>
          </CardFooter>
        </Card>
      )}
    </div>
  );
}

    