import type {Metada<PERSON>} from 'next';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { MindBarProvider } from '@/components/mind-bar';

export const metadata: Metadata = {
  title: 'Eloquent Shepherd',
  description: 'AI-powered sermon preparation for modern pastors.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Alegreya:wght@400;700&family=PT+Sans:wght@400;700&display=swap" rel="stylesheet" />
      </head>
      <body className="font-body antialiased">
        <MindBarProvider>
          {children}
        </MindBarProvider>
        <Toaster />
      </body>
    </html>
  );
}
