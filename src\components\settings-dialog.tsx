"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Settings } from "lucide-react";

export function SettingsDialog() {
  const [contextLength, setContextLength] = useState(4096);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="default"
          size="icon"
          className="fixed bottom-6 right-6 z-50 rounded-full shadow-lg h-14 w-14"
          aria-label="Open AI Settings"
        >
          <Settings className="h-6 w-6" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="font-headline">AI Settings</DialogTitle>
          <DialogDescription>
            Adjust advanced settings for AI generation.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="grid gap-4">
            <Label htmlFor="context-length" className="flex justify-between">
              <span>Context Length</span>
              <span className="text-muted-foreground">{contextLength.toLocaleString()} tokens</span>
            </Label>
            <Slider
              id="context-length"
              min={1024}
              max={16384}
              step={1024}
              value={[contextLength]}
              onValueChange={(value) => setContextLength(value[0])}
            />
            <p className="text-xs text-muted-foreground">
              Controls the maximum number of tokens for the AI model. Higher values allow for longer sermons but may increase cost.
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button type="button">Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
