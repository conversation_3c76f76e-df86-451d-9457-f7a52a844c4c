'use server';

/**
 * @fileOverview Analyzes sermon drafts for tone, clarity, and cultural relevance.
 *
 * - analyzeSermonDraft - A function that handles the sermon draft analysis process.
 * - AnalyzeSermonDraftInput - The input type for the analyzeSermonDraft function.
 * - AnalyzeSermonDraftOutput - The return type for the analyzeSermonDraft function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AnalyzeSermonDraftInputSchema = z.object({
  draftText: z
    .string()
    .describe('The sermon draft text to be analyzed.'),
  systemPrompt: z
    .string()
    .optional()
    .describe('System prompt to guide the analysis. Overrides default system prompt if provided.'),
});
export type AnalyzeSermonDraftInput = z.infer<typeof AnalyzeSermonDraftInputSchema>;

const AnalyzeSermonDraftOutputSchema = z.object({
  toneAnalysis: z
    .string()
    .describe('Analysis of the sermon drafts tone.'),
  clarityAnalysis: z
    .string()
    .describe('Analysis of the sermon drafts clarity.'),
  culturalRelevanceAnalysis: z
    .string()
    .describe('Analysis of the sermon drafts cultural relevance, with a focus on a Nigerian, yet international, audience.'),
  overallFeedback: z
    .string()
    .describe('Overall feedback and suggestions for improvement.'),
});
export type AnalyzeSermonDraftOutput = z.infer<typeof AnalyzeSermonDraftOutputSchema>;

export async function analyzeSermonDraft(
  input: AnalyzeSermonDraftInput
): Promise<AnalyzeSermonDraftOutput> {
  return analyzeSermonDraftFlow(input);
}

const prompt = ai.definePrompt({
  name: 'analyzeSermonDraftPrompt',
  input: {schema: AnalyzeSermonDraftInputSchema},
  output: {schema: AnalyzeSermonDraftOutputSchema},
  prompt: `You are an AI assistant designed to analyze sermon drafts for pastors.

  Analyze the sermon draft provided, paying close attention to its tone, clarity, and cultural relevance for a Nigerian, yet international, audience. Provide specific feedback and suggestions for improvement in each of these areas, as well as overall feedback.

  Draft Text: {{{draftText}}}
  `,
});

const analyzeSermonDraftFlow = ai.defineFlow(
  {
    name: 'analyzeSermonDraftFlow',
    inputSchema: AnalyzeSermonDraftInputSchema,
    outputSchema: AnalyzeSermonDraftOutputSchema,
  },
  async input => {
    const {
      draftText,
      systemPrompt,
    } = input;

    const {
      output,
    } = await prompt({
      draftText,
      systemPrompt,
    });
    return output!;
  }
);
