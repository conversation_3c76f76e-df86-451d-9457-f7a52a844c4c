import type { SVGProps } from "react";

export function Logo(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <title>Eloquent Shepherd Logo</title>
      <path d="M12 22V10" />
      <path d="M15 3.5A2.5 2.5 0 0 0 12.5 1H6a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h1" />
      <path d="M18 10c-2 0-2 2-2 3.5s0 3.5 2 3.5c2.22 0 4-1.78 4-4s-1.78-4-4-4" />
    </svg>
  );
}
