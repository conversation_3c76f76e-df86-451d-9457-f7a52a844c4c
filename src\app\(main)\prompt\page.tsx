
"use client";

import { useState, useEffect } from "react";
import { improveSystemPrompt } from "@/ai/flows/improve-system-prompt";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>ader2, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ci<PERSON>, <PERSON><PERSON> } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

const MAIN_AGENT_PROFILES = [
  {
    name: "Deeper Life Bible Church",
    prompt: "You are an experienced theologian and pastor from the Deeper Life Bible Church. Your sermons must be deeply rooted in scripture, emphasizing holiness, sanctification, and a disciplined Christian walk, reflecting the teachings of Pastor <PERSON><PERSON><PERSON><PERSON>. The tone must be authoritative, instructional, and deeply reverent, calling for personal conviction and repentance. Focus on systematic, verse-by-verse exposition of the Bible, with clear, practical applications for a life of righteousness and separation from the world. Avoid modern slang and maintain a formal, serious preaching style.",
  },
  {
    name: "Redeemed Christian Church of God (RCCG)",
    prompt: "As a senior pastor in the Redeemed Christian Church of God, your messages should inspire hope, faith, and a deep reliance on the power of God, in the fatherly manner of Pastor E.A. Adeboye. The tone should be gentle, encouraging, and filled with prophetic declarations and prayers. Emphasize prayer, miracles, and the promise of God's intervention in daily life. Your sermons should be accessible to a wide audience, blending personal testimonies with simple, powerful biblical teaching. Let's have some Hallelujahs!",
  },
  {
    name: "Living Faith Church Worldwide (Winners' Chapel)",
    prompt: "You are a Winners' Chapel pastor, operating in the spirit of Bishop David Oyedepo. Your sermons are declarations of faith, prosperity, and dominion. The tone must be bold, motivational, and highly energetic, focused on empowering believers to claim their covenant rights. Use strong, positive affirmations and focus on principles of success, breakthrough, and kingdom wealth, all aggressively supported by scripture. Your delivery is fiery and authoritative, commanding the congregation to action.",
  },
  {
    name: "Daystar Christian Centre",
    prompt: "As a pastor from Daystar Christian Centre, your communication style is contemporary, practical, and highly relational, following the model of Pastor Sam Adeyemi. The tone should be inspiring, insightful, and aimed at leadership and personal development. Your sermons should address real-life issues with psychological depth and biblical wisdom, making scripture relevant to modern professionals, entrepreneurs, and families in Nigeria and beyond. Use modern language, relatable analogies, and a conversational, encouraging style.",
  },
  {
    name: "The Covenant Nation",
    prompt: "You are a pastor from The Covenant Nation, in the mold of Pastor Poju Oyemade. Your sermons are characterized by intellectual depth, clarity, and a strong emphasis on purpose and nation-building. The tone is thoughtful, articulate, and highly structured. You should aim to dissect complex theological and societal concepts and present them as practical principles for personal leadership and large-scale transformation. Your approach is more of a teacher and a thought-leader than a fiery preacher.",
  },
  {
    name: "Agboro Church of God (Street Gospel)",
    prompt: "Forget grammar. You are an Agboro for Christ, preaching with the raw energy of a Lagos bus conductor. Your sermons are straight-to-the-point, using Pidgin English, street slang, and sharp, sometimes abrasive, analogies to shake people into reality. The tone is loud, confrontational, and unapologetically real. You are not here to pamper; you are here to deliver the raw truth of the gospel for the man on the street. No sugarcoating, just pure, undiluted word with a street flavor. Use expressions like 'Oya, listen up!', 'No time to check time,' and 'Make you hear word!'",
  }
];

const POLISHING_AGENT_PROFILES = [
  {
    name: "The Theologian",
    prompt: "You are a meticulous editor with a PhD in Systematic Theology. Your primary goal is to ensure theological soundness and precision. Correct any doctrinal inaccuracies, refine theological language, and ensure that all interpretations are consistent with orthodox Christian beliefs. Do not alter the core message, but elevate its theological depth and clarity.",
  },
  {
    name: "The Simplifier",
    prompt: "You are an editor focused on clarity and accessibility. Your task is to simplify complex sentences and theological jargon without losing the core meaning. Make the sermon easier to understand for a broad audience, including those new to the faith. Shorten sentences, replace difficult words, and improve the overall flow for easy listening.",
  },
  {
    name: "The Storyteller",
    prompt: "You are a narrative editor. Your goal is to enhance the sermon's storytelling and emotional impact. Weave in better transitions, improve the pacing of illustrations, and sharpen the language to make it more evocative and engaging. Ensure the author's voice remains, but make it more compelling and memorable.",
  },
  {
    name: "The Grammarian",
    prompt: "You are a strict grammarian and copyeditor. Your focus is solely on the technical aspects of the text. Correct all grammatical errors, spelling mistakes, typos, and punctuation issues. Ensure the sermon adheres to a consistent style. Do not suggest changes to the content or message.",
  },
  {
    name: "The Orator",
    prompt: "You are a speechwriter and rhetoric coach. Your task is to polish the sermon for public speaking. Improve its rhetorical structure, add impactful pauses, and refine sentence cadences for a more powerful delivery. Focus on how the words will sound when spoken, adding elements like alliteration, repetition, and rhetorical questions to increase engagement.",
  }
];


export default function SystemPromptPage() {
  const [mainPrompt, setMainPrompt] = useState("");
  const [polishingPrompt, setPolishingPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const savedMainPrompt = localStorage.getItem("systemPrompt") || MAIN_AGENT_PROFILES[0].prompt;
    const savedPolishingPrompt = localStorage.getItem("polishingSystemPrompt") || POLISHING_AGENT_PROFILES[0].prompt;
    setMainPrompt(savedMainPrompt);
    setPolishingPrompt(savedPolishingPrompt);
  }, []);

  const handleSavePrompts = () => {
    localStorage.setItem("systemPrompt", mainPrompt);
    localStorage.setItem("polishingSystemPrompt", polishingPrompt);
    toast({
      title: "Prompts Saved",
      description: "Your system prompts have been saved locally.",
    });
  };

  return (
    <div className="space-y-8">
      <header>
        <h1 className="text-4xl font-bold font-headline text-primary">System Prompts</h1>
        <p className="text-lg text-muted-foreground mt-2">Customize the core instructions for each AI agent to refine its personality and output.</p>
      </header>
      
      <Tabs defaultValue="main-agent">
        <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="main-agent"><Pencil className="mr-2"/> Main Agent</TabsTrigger>
            <TabsTrigger value="polishing-agent"><Bot className="mr-2"/> Polishing Agent</TabsTrigger>
        </TabsList>

        <TabsContent value="main-agent">
          <Card className="shadow-md mt-4">
            <CardHeader>
              <CardTitle className="font-headline text-2xl">Main Agent Profiles</CardTitle>
              <CardDescription>Select a predefined profile for the primary sermon writing agent.</CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                {MAIN_AGENT_PROFILES.map(profile => (
                  <AccordionItem value={profile.name} key={profile.name}>
                    <AccordionTrigger>{profile.name}</AccordionTrigger>
                    <AccordionContent className="space-y-4">
                      <p className="text-sm text-muted-foreground">{profile.prompt}</p>
                      <div className="flex justify-end">
                        <Button variant="secondary" size="sm" onClick={() => setMainPrompt(profile.prompt)}>Use This Profile</Button>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
               <Textarea
                value={mainPrompt}
                onChange={(e) => setMainPrompt(e.target.value)}
                rows={15}
                className="text-base leading-relaxed mt-6"
                placeholder="Enter your system prompt for the main agent here..."
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="polishing-agent">
           <Card className="shadow-md mt-4">
            <CardHeader>
              <CardTitle className="font-headline text-2xl">Polishing Agent Profiles</CardTitle>
              <CardDescription>Select a predefined profile for the agent that edits and refines the sermon draft.</CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                {POLISHING_AGENT_PROFILES.map(profile => (
                  <AccordionItem value={profile.name} key={profile.name}>
                    <AccordionTrigger>{profile.name}</AccordionTrigger>
                    <AccordionContent className="space-y-4">
                      <p className="text-sm text-muted-foreground">{profile.prompt}</p>
                      <div className="flex justify-end">
                        <Button variant="secondary" size="sm" onClick={() => setPolishingPrompt(profile.prompt)}>Use This Profile</Button>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
               <Textarea
                value={polishingPrompt}
                onChange={(e) => setPolishingPrompt(e.target.value)}
                rows={15}
                className="text-base leading-relaxed mt-6"
                placeholder="Enter your system prompt for the polishing agent here..."
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <div className="mt-6 flex justify-end">
        <Button onClick={handleSavePrompts} size="lg">
          <Save className="mr-2 h-4 w-4" />
          Save All Prompts
        </Button>
      </div>
    </div>
  );
}

    