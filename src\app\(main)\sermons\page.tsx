import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MoreVertical, PlusCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";

const mockSermons = [
  { id: "1", title: "The Power of Forgiveness", topic: "Matthew 6:14-15", date: "2024-07-21", tags: ["Grace", "New Testament"] },
  { id: "2", title: "Walking in Faith", topic: "Hebrews 11", date: "2024-07-14", tags: ["Faith", "Old Testament Heroes"] },
  { id: "3", title: "The Good Samaritan", topic: "Luke 10:25-37", date: "2024-07-07", tags: ["<PERSON>bles", "Love", "Compassion"] },
  { id: "4", title: "Fruits of the Spirit", topic: "Galatians 5:22-23", date: "2024-06-30", tags: ["Christian Living", "Holy Spirit"] },
  { id: "5", title: "The Prodigal Son", topic: "Luke 15:11-32", date: "2024-06-23", tags: ["Redemption", "Parables", "Fatherhood"] },
];

export default function SermonArchivePage() {
  return (
    <div className="space-y-8">
      <header className="flex items-center justify-between gap-4">
        <div>
          <h1 className="text-4xl font-bold font-headline text-primary">Sermon Archive</h1>
          <p className="text-lg text-muted-foreground mt-2">Your library of past and in-progress sermons.</p>
        </div>
        <Button size="lg">
          <PlusCircle className="mr-2 h-5 w-5" />
          Add New Sermon
        </Button>
      </header>
      <Card className="shadow-md">
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[30%]">Title</TableHead>
                <TableHead>Topic / Scripture</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Tags</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockSermons.map((sermon) => (
                <TableRow key={sermon.id}>
                  <TableCell className="font-medium text-primary">{sermon.title}</TableCell>
                  <TableCell>{sermon.topic}</TableCell>
                  <TableCell>{sermon.date}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-2">
                      {sermon.tags.map(tag => <Badge key={tag} variant="secondary">{tag}</Badge>)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">More actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>View/Edit</DropdownMenuItem>
                        <DropdownMenuItem>Duplicate</DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive focus:text-destructive">Delete</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
