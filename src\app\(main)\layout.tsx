import { Sidebar<PERSON>rovider, Sidebar, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bar<PERSON>eader, SidebarContent } from "@/components/ui/sidebar";
import { SidebarNav } from "@/components/layout/sidebar-nav";
import { SettingsDialog } from "@/components/settings-dialog";
import { Logo } from "@/components/icons";
import { MindBar } from "@/components/mind-bar";

export default function MainLayout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <Sidebar>
        <SidebarHeader>
          <div className="flex items-center gap-3 px-2 py-1">
            <Logo className="size-8 text-sidebar-primary shrink-0" />
            <h1 className="text-xl font-bold text-sidebar-primary font-headline truncate">
              Eloquent Shepherd
            </h1>
          </div>
        </SidebarHeader>
        <SidebarContent className="p-2">
          <SidebarNav />
        </SidebarContent>
      </Sidebar>
      <SidebarInset>
        <div className="min-h-screen p-4 md:p-8">
          {children}
        </div>
        <MindBar />
        <SettingsDialog />
      </SidebarInset>
    </SidebarProvider>
  );
}
