# **App Name**: Eloquent <PERSON>

## Core Features:

- <PERSON>mon Outline Generator: AI-powered sermon outline generation based on scripture or selected topics, providing multiple thematic options and key points to cover.
- Contextual Insights: Integrated Bible verse lookup and commentary suggestions tool for deeper insights, automatically suggesting relevant passages and interpretations based on the sermon outline.
- Message Analyzer: AI analysis tool that evaluates sermon drafts for tone, clarity, and cultural relevance (with a focus on a Nigerian, yet international, audience), suggesting improvements for more engaging delivery.
- Sermon Archive: Secure sermon management and archiving system, allowing pastors to organize and store sermon drafts, notes, and resources.
- Prompt Editor and Estimator: A settings panel allowing the user to edit the system prompt, and displays the estimated prompt token usage of the prompt
- AI prompt enhancer: An *Improve Prompt* button that analyzes the current prompt and provides improved prompts that provide better generation
- Token Configuration: Settings icon that gives control of the the model context limit for advanced usage, and allows adjusting token estimation

## Style Guidelines:

- Primary color: Deep blue (#30475E) to evoke thoughtfulness and reverence.
- Background color: Light gray (#E8E8E8) to provide a neutral and calming backdrop.
- Accent color: Warm gray (#8D8D8D) for interactive elements and highlights, providing a subtle contrast.
- Headline font: '<PERSON><PERSON><PERSON><PERSON>', a humanist serif, provides an elegant, intellectual, contemporary feel. Body Font: 'PT Sans' for the main text; a legible, modern sans-serif for readability.
- Simple, line-based icons representing key theological concepts, designed in a style that is both modern and timeless.
- Clean and organized layout with clear hierarchy, promoting ease of navigation and focus on content.