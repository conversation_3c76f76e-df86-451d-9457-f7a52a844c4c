'use server';

/**
 * @fileOverview A flow for suggesting relevant Bible passages and interpretations based on a sermon outline.
 *
 * - contextualInsights - A function that suggests Bible passages and interpretations.
 * - ContextualInsightsInput - The input type for the contextualInsights function.
 * - ContextualInsightsOutput - The return type for the contextualInsights function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ContextualInsightsInputSchema = z.object({
  sermonOutline: z
    .string()
    .describe('The sermon outline to generate contextual insights for.'),
});
export type ContextualInsightsInput = z.infer<typeof ContextualInsightsInputSchema>;

const ContextualInsightsOutputSchema = z.object({
  biblePassages: z
    .string()
    .describe('Relevant Bible passages based on the sermon outline.'),
  commentarySuggestions: z
    .string()
    .describe('Commentary suggestions for deeper understanding.'),
});
export type ContextualInsightsOutput = z.infer<typeof ContextualInsightsOutputSchema>;

export async function contextualInsights(input: ContextualInsightsInput): Promise<ContextualInsightsOutput> {
  return contextualInsightsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'contextualInsightsPrompt',
  input: {schema: ContextualInsightsInputSchema},
  output: {schema: ContextualInsightsOutputSchema},
  prompt: `You are an expert in biblical scripture and theology. Based on the provided sermon outline, suggest relevant Bible passages and commentary suggestions.

Sermon Outline: {{{sermonOutline}}}

Provide the Bible passages and commentary suggestions in a clear and organized manner.`,
});

const contextualInsightsFlow = ai.defineFlow(
  {
    name: 'contextualInsightsFlow',
    inputSchema: ContextualInsightsInputSchema,
    outputSchema: ContextualInsightsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
